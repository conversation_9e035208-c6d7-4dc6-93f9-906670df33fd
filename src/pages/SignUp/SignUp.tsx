import React, { useState } from 'react';
import { useClerk } from '@clerk/clerk-react';
import { Link } from 'react-router-dom';
import { Jobb<PERSON>ogg<PERSON>ogo } from '../../components/ui';

const SignUp: React.FC = () => {
  const clerk = useClerk();

  // Waitlist form state
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasExistingAccount, setHasExistingAccount] = useState(false);

  // Handle waitlist form submission
  const handleWaitlistSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email.trim()) {
      setError('E-postadresse er påkrevd');
      return;
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError('Vennligst oppgi en gyldig e-postadresse');
      return;
    }

    setIsSubmitting(true);
    setError(null);
    setHasExistingAccount(false);

    try {
      // Use Clerk's official joinWaitlist method
      await clerk.joinWaitlist({
        emailAddress: email,
      });

      setIsSubmitted(true);
    } catch (err: any) {
      console.error('Waitlist submission error:', err);

      // Handle specific error cases based on Clerk's error codes
      const errorCode = err.errors?.[0]?.code || err.code;
      const errorMessage = err.errors?.[0]?.message || err.message || '';
      const longMessage = err.errors?.[0]?.longMessage || '';

      if (errorCode === 'form_identifier_exists' ||
          errorCode === 'form_identifier_exists__email_address' ||
          errorMessage.includes('identifier already exists') ||
          longMessage.includes('identifier already exists')) {
        setError('Denne e-postadressen er allerede registrert på ventelisten');
      } else if (errorCode === 'form_identifier_not_allowed') {
        setError('Denne e-postadressen er ikke tillatt');
      } else if (errorCode === 'user_already_exists' ||
                 errorMessage.includes('already exists') ||
                 errorMessage.includes('user exists') ||
                 longMessage.includes('user exists')) {
        setError('Det finnes allerede en brukerkonto med denne e-postadressen.');
        setHasExistingAccount(true);
      } else if (errorCode === 'waitlist_full' || errorMessage.includes('waitlist is full')) {
        setError('Ventelisten er for øyeblikket full. Prøv igjen senere.');
      } else if (errorCode === 'invalid_email_address' || errorMessage.includes('invalid email')) {
        setError('Vennligst oppgi en gyldig e-postadresse');
      } else if (errorMessage.includes('rate limit') || errorCode === 'rate_limit_exceeded') {
        setError('For mange forsøk. Vennligst vent litt før du prøver igjen.');
      } else {
        // Fallback error message with more details for debugging
        setError(`Noe gikk galt. Vennligst prøv igjen senere. (${errorCode || 'unknown error'})`);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-white flex flex-col lg:flex-row">
      {/* Left Side - Brand & Context Section */}
      <div className="lg:w-1/2 relative bg-gradient-to-br from-jobblogg-accent via-jobblogg-accent-dark to-jobblogg-accent-light overflow-hidden">
        {/* Background Pattern/Texture */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }} />
        </div>

        {/* Content Container */}
        <div className="relative z-10 h-full flex flex-col justify-between p-8 lg:p-12 min-h-[400px] lg:min-h-screen">
          {/* Logo and Header */}
          <div className="space-y-8">
            {/* Logo */}
            <div className="animate-fade-in">
              <Link
                to="/"
                className="inline-flex bg-white/95 backdrop-blur-sm rounded-xl px-4 py-3 shadow-soft border border-white/20 hover:bg-white hover:shadow-medium transition-all duration-200 cursor-pointer"
                aria-label="Gå til forsiden"
              >
                <JobbLoggLogo size="lg" />
              </Link>
            </div>

            {/* Value Proposition */}
            <div className="space-y-4 animate-slide-up" style={{ animationDelay: '200ms' }}>
              <h1 className="text-3xl lg:text-4xl xl:text-5xl font-bold text-white leading-tight">
                Gjør prosjekthverdagen<br />
                enklere.
              </h1>
              <p className="text-lg lg:text-xl text-white/90 max-w-md leading-relaxed">
                Bli med tusenvis av håndverkere som allerede bruker JobbLogg for profesjonell dokumentasjon og kundeoppfølging.
              </p>
            </div>
          </div>

          {/* Use Case Example */}
          <div className="animate-slide-up" style={{ animationDelay: '400ms' }}>
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </div>
                </div>
                <div>
                  <h3 className="text-white font-semibold text-base lg:text-lg mb-3">Baderomsoppussing - uke 2</h3>
                  <div className="space-y-2 text-sm lg:text-base">
                    <div className="flex items-start space-x-2">
                      <span className="text-white/70 font-medium">Mandag</span>
                      <span className="text-white/95">"Startet riving av gamle fliser 📸"</span>
                    </div>
                    <div className="flex items-start space-x-2">
                      <span className="text-white/70 font-medium">Onsdag</span>
                      <span className="text-white/95">"Oppdaget fuktskade - diskuterer løsning"</span>
                    </div>
                    <div className="flex items-start space-x-2">
                      <span className="text-white/70 font-medium">Fredag</span>
                      <span className="text-white/95">"Nye fliser på plass! Se resultatet 🎉"</span>
                    </div>
                  </div>
                  <div className="text-white/70 text-xs mt-3 italic">
                    Transparent kommunikasjon gir trygghet og tillit
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Right Side - Sign-up Section */}
      <div className="lg:w-1/2 flex flex-col items-center justify-center min-h-screen lg:min-h-full p-6 sm:p-8 lg:p-12 xl:p-16">
        <div className="w-full max-w-md space-y-8">
          {/* Custom Waitlist Form */}
          <div className="animate-scale-in" style={{ animationDelay: '300ms' }}>
            <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-8 space-y-6">
              {/* Header */}
              {!isSubmitted && (
                <div className="text-center space-y-3">
                  <h1 className="text-2xl font-bold text-jobblogg-text-strong">
                    Bli med på ventelisten
                  </h1>
                  <p className="text-jobblogg-text-medium">
                    Vi tar imot nye brukere gradvis. Registrer deg for å bli varslet når du kan komme i gang med JobbLogg!
                  </p>
                </div>
              )}

              {/* Success State */}
              {isSubmitted ? (
                <div className="text-center space-y-6">
                  <div className="w-20 h-20 bg-jobblogg-accent/10 rounded-full flex items-center justify-center mx-auto animate-scale-in">
                    <svg className="w-10 h-10 text-jobblogg-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <div className="space-y-3">
                    <h3 className="text-xl font-bold text-jobblogg-text-strong">
                      Velkommen til ventelisten! 🎉
                    </h3>
                    <div className="space-y-2">
                      <p className="text-jobblogg-text-strong font-medium">
                        Du er nå registrert med e-postadressen:
                      </p>
                      <p className="text-jobblogg-accent font-semibold bg-jobblogg-accent/5 px-3 py-2 rounded-lg">
                        {email}
                      </p>
                    </div>
                    <p className="text-jobblogg-text-medium leading-relaxed">
                      Vi sender deg en e-post så snart du kan komme i gang med JobbLogg.
                      Hold øye med innboksen din!
                    </p>
                  </div>
                  <div className="pt-2 space-y-3">
                    <Link
                      to="/"
                      className="inline-flex items-center px-6 py-3 bg-jobblogg-accent hover:bg-jobblogg-accent-dark text-white font-semibold rounded-xl transition-colors duration-200"
                    >
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                      </svg>
                      Gå til forsiden
                    </Link>
                    <div className="text-xs text-jobblogg-text-muted">
                      Du vil motta en bekreftelse på e-post innen kort tid
                    </div>
                  </div>
                </div>
              ) : (
                /* Waitlist Form */
                <form onSubmit={handleWaitlistSubmit} className="space-y-6">
                  <div className="space-y-2">
                    <label htmlFor="email" className="block text-sm font-medium text-jobblogg-text-strong">
                      E-postadresse
                    </label>
                    <input
                      type="email"
                      id="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="w-full px-4 py-3 border border-jobblogg-border rounded-xl focus:ring-2 focus:ring-jobblogg-accent/20 focus:border-jobblogg-accent transition-colors duration-200 text-jobblogg-text-strong placeholder-jobblogg-text-muted"
                      placeholder="<EMAIL>"
                      required
                      disabled={isSubmitting}
                    />
                    {error && (
                      <div className="mt-2">
                        <div className="flex items-start space-x-2">
                          <svg className="w-4 h-4 text-red-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <p className="text-sm text-red-600">{error}</p>
                        </div>
                        {hasExistingAccount && (
                          <div className="mt-3 pl-6">
                            <Link
                              to="/sign-in"
                              className="inline-flex items-center text-sm font-medium text-jobblogg-accent hover:text-jobblogg-accent-dark transition-colors duration-200"
                            >
                              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                              </svg>
                              Gå til innlogging
                            </Link>
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full bg-jobblogg-accent hover:bg-jobblogg-accent-dark disabled:opacity-50 disabled:cursor-not-allowed text-white font-semibold py-3 px-6 rounded-xl transition-colors duration-200 flex items-center justify-center space-x-2"
                  >
                    {isSubmitting ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span>Registrerer...</span>
                      </>
                    ) : (
                      <span>Bli med på ventelisten</span>
                    )}
                  </button>
                </form>
              )}

              {/* Footer */}
              <div className="text-center pt-4 border-t border-jobblogg-border">
                <p className="text-xs text-jobblogg-text-muted">
                  Sikret av{' '}
                  <span className="font-medium">Clerk</span>
                </p>
              </div>
            </div>
          </div>

          {/* Footer Links */}
          <div className="text-center animate-slide-up" style={{ animationDelay: '500ms' }}>
            <div className="flex items-center justify-center space-x-6 text-sm text-jobblogg-text-muted">
              <Link
                to="/privacy-policy"
                className="hover:text-jobblogg-accent transition-colors duration-200"
              >
                Personvern
              </Link>
              <span className="text-jobblogg-border">•</span>
              <Link
                to="/terms-of-service"
                className="hover:text-jobblogg-accent transition-colors duration-200"
              >
                Bruksvilkår
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SignUp;
